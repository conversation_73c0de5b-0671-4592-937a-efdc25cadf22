package cn.facilityone.ai.service.biz;

import cn.facilityone.ai.dto.ReportAnalysisResult;
import cn.facilityone.ai.dto.WorkOrderAnalysisResult;
import cn.facilityone.ai.dto.WorkOrderData;
import cn.facilityone.ai.entity.FileEntity;
import cn.facilityone.ai.entity.RestResult;
import cn.facilityone.ai.exception.AIServiceException;
import cn.facilityone.ai.exception.CSVParseException;
import cn.facilityone.ai.exception.ReportAnalysisException;
import cn.facilityone.ai.exception.UnsupportedFileFormatException;
import cn.facilityone.ai.service.db.FileService;
import cn.facilityone.ai.service.tool.ChartTool;
import cn.facilityone.ai.util.PromptsUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.client.ResourceAccessException;

import java.io.File;
import java.io.FileNotFoundException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报表分析业务服务
 * 负责处理基于文件ID的报表分析功能
 */
@Slf4j
@Service
public class ReportBizService {

    private final FileService fileService;
    private final FileBizService fileBizService;
    private final ChatClient chatClient;
    private final ChartTool chartTool;

    // CSV文件列名映射
    private static final Map<String, String> CSV_COLUMN_MAPPING = Map.ofEntries(
            Map.entry("工单号", "orderNumber"),
            Map.entry("申请人", "applicant"),
            Map.entry("创建日期", "createDate"),
            Map.entry("创建时间", "createTime"),
            Map.entry("发布人", "publisher"),
            Map.entry("发布时间", "publishTime"),
            Map.entry("执行人", "executor"),
            Map.entry("接单时间", "acceptTime"),
            Map.entry("服务类型", "serviceType"),
            Map.entry("优先级", "priority"),
            Map.entry("部门", "department"),
            Map.entry("大厦", "building"),
            Map.entry("楼层", "floor"),
            Map.entry("房间", "room"),
            Map.entry("完整位置", "fullLocation"),
            Map.entry("描述", "description"),
            Map.entry("工作内容", "workContent"),
            Map.entry("工单状态", "status"),
            Map.entry("暂停原因", "pauseReason"),
            Map.entry("收费情况", "chargeStatus"),
            Map.entry("预估开始时间", "estimatedStartTime"),
            Map.entry("预估结束时间", "estimatedEndTime"),
            Map.entry("实际开始时间", "actualStartTime"),
            Map.entry("实际结束时间", "actualEndTime"),
            Map.entry("工作时长", "workDurationMinutes")
    );

    @Autowired
    public ReportBizService(FileService fileService,
                            FileBizService fileBizService,
                            @Qualifier("chatClient") ChatClient chatClient,
                            ChartTool chartTool) {
        this.fileService = fileService;
        this.fileBizService = fileBizService;
        this.chatClient = chatClient;
        this.chartTool = chartTool;
    }

    /**
     * 根据文件ID分析报表
     *
     * @param fileId 文件ID
     * @return 分析结果
     * @throws FileNotFoundException          文件不存在
     * @throws UnsupportedFileFormatException 不支持的文件格式
     * @throws CSVParseException              CSV解析异常
     * @throws AIServiceException             AI服务异常
     * @throws ReportAnalysisException        报表分析异常
     */
    public ReportAnalysisResult analyzeReportByFileId(Long fileId) throws FileNotFoundException {
        // 参数验证
        if (fileId == null || fileId <= 0) {
            log.warn("报表分析请求参数无效 - 文件ID: {}", fileId);
            throw new ReportAnalysisException("文件ID不能为空且必须大于0", fileId, "PARAMETER_VALIDATION", "INVALID_FILE_ID");
        }
        long startTime = System.currentTimeMillis();
        String analysisId = generateAnalysisId(fileId);

        log.info("开始分析报表 - 分析ID: {}, 文件ID: {}, 开始时间: {}",
                analysisId, fileId, LocalDateTime.now());

        try {
            // 1. 参数验证
            validateAnalysisParameters(fileId, analysisId);

            // 2. 获取文件实体
            FileEntity fileEntity = getFileEntityWithValidation(fileId, analysisId);

            // 3. 验证文件格式
            validateFileFormatWithLogging(fileEntity, analysisId);

            // 4. 获取文件对象
            File csvFile = getPhysicalFileWithValidation(fileEntity, analysisId);

            // 5. 解析CSV文件
            List<WorkOrderData> workOrderList = parseCSVFileWithEnhancedErrorHandling(csvFile, analysisId);
            log.info("CSV文件解析完成 - 分析ID: {}, 解析数据量: {}, 有效数据量: {}",
                    analysisId, workOrderList.size(), workOrderList.size());

            // 6. 执行统计分析
            WorkOrderAnalysisResult statisticalResult = performStatisticalAnalysisWithErrorHandling(workOrderList, analysisId);

            // 7. 生成AI分析报告
            String promptTemplate = "fm/report-analysis3";
            String aiAnalysis = generateAIAnalysisWithEnhancedErrorHandling(statisticalResult, promptTemplate, analysisId);

            // 8. 构建返回结果
            ReportAnalysisResult result = buildAnalysisResult(
                    statisticalResult, aiAnalysis, fileEntity, workOrderList.size(),
                    startTime, analysisId);

            long totalProcessingTime = System.currentTimeMillis() - startTime;
            log.info("报表分析成功完成 - 分析ID: {}, 文件ID: {}, 处理数据量: {}, 总耗时: {}ms, 完成时间: {}",
                    analysisId, fileId, workOrderList.size(), totalProcessingTime, LocalDateTime.now());

            return result;

        } catch (FileNotFoundException e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("报表分析失败 - 文件不存在 - 分析ID: {}, 文件ID: {}, 耗时: {}ms, 错误: {}",
                    analysisId, fileId, processingTime, e.getMessage());
            throw e;

        } catch (UnsupportedFileFormatException e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("报表分析失败 - 文件格式不支持 - 分析ID: {}, 文件ID: {}, 耗时: {}ms, 详细错误: {}",
                    analysisId, fileId, processingTime, e.getDetailedMessage());
            throw e;

        } catch (CSVParseException e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("报表分析失败 - CSV解析异常 - 分析ID: {}, 文件ID: {}, 耗时: {}ms, 详细错误: {}",
                    analysisId, fileId, processingTime, e.getDetailedMessage());
            throw new ReportAnalysisException("CSV文件解析失败: " + e.getMessage(), fileId, "CSV_PARSING", "PARSE_ERROR", e);

        } catch (AIServiceException e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("报表分析失败 - AI服务异常 - 分析ID: {}, 文件ID: {}, 耗时: {}ms, 详细错误: {}",
                    analysisId, fileId, processingTime, e.getDetailedMessage());
            throw new ReportAnalysisException("AI分析服务异常: " + e.getMessage(), fileId, "AI_ANALYSIS", "SERVICE_ERROR", e);

        } catch (ReportAnalysisException e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("报表分析失败 - 业务逻辑异常 - 分析ID: {}, 文件ID: {}, 耗时: {}ms, 详细错误: {}",
                    analysisId, fileId, processingTime, e.getDetailedMessage());
            throw e;

        } catch (RuntimeException e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("报表分析失败 - 运行时异常 - 分析ID: {}, 文件ID: {}, 耗时: {}ms, 错误: {}",
                    analysisId, fileId, processingTime, e.getMessage(), e);
            throw new ReportAnalysisException("报表分析运行时异常: " + e.getMessage(), fileId, "RUNTIME", "SYSTEM_ERROR", e);

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("报表分析失败 - 未知异常 - 分析ID: {}, 文件ID: {}, 耗时: {}ms, 错误: {}",
                    analysisId, fileId, processingTime, e.getMessage(), e);
            throw new ReportAnalysisException("报表分析未知异常: " + e.getMessage(), fileId, "UNKNOWN", "UNKNOWN_ERROR", e);
        }
    }

    /**
     * 验证文件格式
     *
     * @param fileEntity 文件实体
     * @throws UnsupportedFileFormatException 不支持的文件格式
     */
    private void validateFileFormat(FileEntity fileEntity) {
        String fileName = fileEntity.getName();
        if (StrUtil.isBlank(fileName)) {
            throw new UnsupportedFileFormatException("文件名为空", fileName, "unknown");
        }

        // 检查文件扩展名
        String extension = "";
        if (fileName.contains(".")) {
            extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        }

        if (!"csv".equals(extension)) {
            log.warn("不支持的文件格式，文件名: {}, 扩展名: {}", fileName, extension);
            throw new UnsupportedFileFormatException(
                    "不支持的文件格式，仅支持CSV文件",
                    fileName,
                    extension
            );
        }

        // 检查文件类型
        if (fileEntity.getType() != FileEntity.FileType.DOCUMENT &&
                fileEntity.getType() != FileEntity.FileType.OTHER) {
            log.warn("文件类型不匹配，文件名: {}, 文件类型: {}", fileName, fileEntity.getType());
        }
    }

    /**
     * 解析CSV文件
     *
     * @param csvFile CSV文件
     * @return 工单数据列表
     * @throws CSVParseException CSV解析异常
     */
    private List<WorkOrderData> parseCSVFile(File csvFile) {
        log.info("开始解析CSV文件: {}, 文件大小: {} bytes", csvFile.getName(), csvFile.length());

        List<WorkOrderData> workOrderList = new ArrayList<>();
        int totalRows = 0;
        int successfulRows = 0;
        int failedRows = 0;

        try {
            // 使用Hutool的CsvReader读取CSV文件
            CsvReader reader = CsvUtil.getReader();
            cn.hutool.core.text.csv.CsvData csvData = reader.read(csvFile.toPath(), StandardCharsets.UTF_8);

            if (csvData == null) {
                log.error("CSV数据读取失败，文件: {}", csvFile.getName());
                throw new CSVParseException("CSV文件读取失败，数据为空", csvFile.getName());
            }

            totalRows = csvData.getRowCount();
            if (totalRows == 0) {
                log.warn("CSV文件无数据行，文件: {}", csvFile.getName());
                return workOrderList;
            }

            // 获取表头
            cn.hutool.core.text.csv.CsvRow headerRow = csvData.getRow(0);
            String[] headers = headerRow.toArray(new String[0]);
            log.info("CSV表头解析完成，文件: {}, 表头数量: {}, 表头内容: {}",
                    csvFile.getName(), headers.length, Arrays.toString(headers));

            // 解析数据行
            for (int i = 1; i < totalRows; i++) {
                cn.hutool.core.text.csv.CsvRow dataRow = csvData.getRow(i);
                String[] row = dataRow.toArray(new String[0]);

                try {
                    WorkOrderData workOrder = parseWorkOrderFromRow(headers, row, i + 1);
                    if (workOrder != null) {
                        workOrderList.add(workOrder);
                        successfulRows++;
                    }
                } catch (Exception e) {
                    failedRows++;
                    log.warn("解析CSV行失败，文件: {}, 行号: {}, 错误: {}, 行数据: {}",
                            csvFile.getName(), i + 1, e.getMessage(), Arrays.toString(row));

                    // 如果失败行数过多，抛出异常
                    if (failedRows > (totalRows - 1) * 0.5) { // 超过50%的行解析失败
                        log.error("CSV解析失败行数过多，文件: {}, 总行数: {}, 失败行数: {}, 失败率: {}%",
                                csvFile.getName(), totalRows - 1, failedRows, (failedRows * 100.0 / (totalRows - 1)));
                        throw new CSVParseException("CSV文件解析失败行数过多，可能文件格式不正确",
                                csvFile.getName(), i + 1);
                    }
                }
            }

            log.info("CSV文件解析完成，文件: {}, 总行数: {}, 成功解析: {}, 失败行数: {}, 成功率: {}%",
                    csvFile.getName(), totalRows - 1, successfulRows, failedRows,
                    successfulRows > 0 ? (successfulRows * 100.0 / (totalRows - 1)) : 0);

            if (successfulRows == 0) {
                log.error("CSV文件无有效数据，文件: {}", csvFile.getName());
                throw new CSVParseException("CSV文件无有效数据行", csvFile.getName());
            }

        } catch (CSVParseException e) {
            throw e;
        } catch (Exception e) {
            log.error("CSV文件解析异常，文件: {}, 错误: {}", csvFile.getName(), e.getMessage(), e);
            throw new CSVParseException("CSV文件解析失败: " + e.getMessage(), csvFile.getName(), null, null, e);
        }

        return workOrderList;
    }

    /**
     * 从CSV行数据解析工单对象
     *
     * @param headers   表头数组
     * @param row       数据行数组
     * @param rowNumber 行号（用于日志）
     * @return 工单数据对象
     */
    private WorkOrderData parseWorkOrderFromRow(String[] headers, String[] row, int rowNumber) {
        WorkOrderData workOrder = new WorkOrderData();

        for (int i = 0; i < headers.length && i < row.length; i++) {
            String header = headers[i].trim();
            String value = row[i].trim();

            // 跳过空值
            if (StrUtil.isBlank(value)) {
                continue;
            }

            // 根据表头映射设置字段值
            try {
                setWorkOrderField(workOrder, header, value);
            } catch (Exception e) {
                log.debug("设置字段失败，表头: {}, 值: {}, 行号: {}, 错误: {}",
                        header, value, rowNumber, e.getMessage());
            }
        }

        return workOrder;
    }

    /**
     * 设置工单对象字段值
     *
     * @param workOrder 工单对象
     * @param header    表头名称
     * @param value     字段值
     */
    private void setWorkOrderField(WorkOrderData workOrder, String header, String value) {
        String fieldName = CSV_COLUMN_MAPPING.get(header);
        if (StrUtil.isBlank(fieldName)) {
            return; // 未映射的字段跳过
        }

        switch (fieldName) {
            case "orderNumber" -> workOrder.setOrderNumber(value);
            case "applicant" -> workOrder.setApplicant(value);
            case "createDate" -> workOrder.setCreateDate(parseDate(value));
            case "createTime" -> workOrder.setCreateTime(parseTime(value));
            case "publisher" -> workOrder.setPublisher(value);
            case "publishTime" -> workOrder.setPublishTime(parseTime(value));
            case "executor" -> workOrder.setExecutor(value);
            case "acceptTime" -> workOrder.setAcceptTime(parseTime(value));
            case "serviceType" -> workOrder.setServiceType(value);
            case "priority" -> workOrder.setPriority(value);
            case "department" -> workOrder.setDepartment(value);
            case "building" -> workOrder.setBuilding(value);
            case "floor" -> workOrder.setFloor(value);
            case "room" -> workOrder.setRoom(value);
            case "fullLocation" -> workOrder.setFullLocation(value);
            case "description" -> workOrder.setDescription(value);
            case "workContent" -> workOrder.setWorkContent(value);
            case "status" -> workOrder.setStatus(value);
            case "pauseReason" -> workOrder.setPauseReason(value);
            case "chargeStatus" -> workOrder.setChargeStatus(value);
            case "estimatedStartTime" -> workOrder.setEstimatedStartTime(parseTime(value));
            case "estimatedEndTime" -> workOrder.setEstimatedEndTime(parseTime(value));
            case "actualStartTime" -> workOrder.setActualStartTime(parseTime(value));
            case "actualEndTime" -> workOrder.setActualEndTime(parseTime(value));
            case "workDurationMinutes" -> workOrder.setWorkDurationMinutes(parseInteger(value));
        }
    }

    /**
     * 解析日期字符串
     *
     * @param dateStr 日期字符串
     * @return LocalDate对象
     */
    private LocalDate parseDate(String dateStr) {
        if (StrUtil.isBlank(dateStr)) {
            return null;
        }

        try {
            // 尝试多种日期格式
            String[] patterns = {"dd/MM/yy", "dd/MM/yyyy", "yyyy-MM-dd", "yyyy/MM/dd"};

            for (String pattern : patterns) {
                try {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                    return LocalDate.parse(dateStr, formatter);
                } catch (DateTimeParseException ignored) {
                    // 继续尝试下一个格式
                }
            }

            log.debug("无法解析日期格式: {}", dateStr);
            return null;

        } catch (Exception e) {
            log.debug("日期解析异常: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析时间字符串
     *
     * @param timeStr 时间字符串
     * @return LocalTime对象
     */
    private LocalTime parseTime(String timeStr) {
        if (StrUtil.isBlank(timeStr)) {
            return null;
        }

        try {
            // 尝试多种时间格式
            String[] patterns = {"HH:mm:ss", "HH:mm", "H:mm:ss", "H:mm"};

            for (String pattern : patterns) {
                try {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                    return LocalTime.parse(timeStr, formatter);
                } catch (DateTimeParseException ignored) {
                    // 继续尝试下一个格式
                }
            }

            log.debug("无法解析时间格式: {}", timeStr);
            return null;

        } catch (Exception e) {
            log.debug("时间解析异常: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析整数字符串
     *
     * @param intStr 整数字符串
     * @return Integer对象
     */
    private Integer parseInteger(String intStr) {
        if (StrUtil.isBlank(intStr)) {
            return null;
        }

        try {
            return Integer.parseInt(intStr.trim());
        } catch (NumberFormatException e) {
            log.debug("整数解析异常: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 执行统计分析
     *
     * @param workOrderList 工单数据列表
     * @return 统计分析结果
     */
    private WorkOrderAnalysisResult performStatisticalAnalysis(List<WorkOrderData> workOrderList) {
        log.info("开始执行统计分析，数据量: {}", workOrderList.size());

        WorkOrderAnalysisResult result = new WorkOrderAnalysisResult();

        if (CollectionUtil.isEmpty(workOrderList)) {
            log.warn("工单数据为空，返回空的统计结果");
            result.setTotalCount(0);
            return result;
        }

        // 基础统计
        result.setTotalCount(workOrderList.size());

        // 状态分布统计
        result.setStatusDistribution(
                workOrderList.stream()
                        .filter(wo -> StrUtil.isNotBlank(wo.getStatus()))
                        .collect(Collectors.groupingBy(
                                WorkOrderData::getStatus,
                                Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                        ))
        );

        // 优先级分布统计
        result.setPriorityDistribution(
                workOrderList.stream()
                        .filter(wo -> StrUtil.isNotBlank(wo.getPriority()))
                        .collect(Collectors.groupingBy(
                                WorkOrderData::getPriority,
                                Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                        ))
        );

        // 服务类型分布统计
        result.setServiceTypeDistribution(
                workOrderList.stream()
                        .filter(wo -> StrUtil.isNotBlank(wo.getServiceType()))
                        .collect(Collectors.groupingBy(
                                WorkOrderData::getServiceType,
                                Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                        ))
        );

        // 部门分布统计
        result.setDepartmentDistribution(
                workOrderList.stream()
                        .filter(wo -> StrUtil.isNotBlank(wo.getDepartment()))
                        .collect(Collectors.groupingBy(
                                WorkOrderData::getDepartment,
                                Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                        ))
        );

        // 大厦分布统计
        result.setBuildingDistribution(
                workOrderList.stream()
                        .filter(wo -> StrUtil.isNotBlank(wo.getBuilding()))
                        .collect(Collectors.groupingBy(
                                WorkOrderData::getBuilding,
                                Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                        ))
        );

        // 时间维度分析
        analyzeTimeDistribution(workOrderList, result);

        // 处理时长分析
        analyzeProcessingTime(workOrderList, result);

        // 完成率分析
        analyzeCompletionRate(workOrderList, result);

        // 收费情况分析
        analyzeChargeStatus(workOrderList, result);

        log.info("统计分析完成");
        return result;
    }

    /**
     * 分析时间分布
     *
     * @param workOrderList 工单数据列表
     * @param result        分析结果对象
     */
    private void analyzeTimeDistribution(List<WorkOrderData> workOrderList, WorkOrderAnalysisResult result) {
        // 月度分布统计
        result.setMonthlyDistribution(
                workOrderList.stream()
                        .filter(wo -> wo.getCreateDate() != null)
                        .collect(Collectors.groupingBy(
                                wo -> String.format("%02d", wo.getCreateDate().getMonthValue()),
                                Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                        ))
        );

        // 周分布统计
        result.setWeeklyDistribution(
                workOrderList.stream()
                        .filter(wo -> wo.getCreateDate() != null)
                        .collect(Collectors.groupingBy(
                                wo -> String.valueOf(wo.getCreateDate().getDayOfWeek().getValue()),
                                Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                        ))
        );

        // 小时分布统计
        result.setHourlyDistribution(
                workOrderList.stream()
                        .filter(wo -> wo.getCreateTime() != null)
                        .collect(Collectors.groupingBy(
                                wo -> String.format("%02d", wo.getCreateTime().getHour()),
                                Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                        ))
        );
    }

    /**
     * 分析处理时长
     *
     * @param workOrderList 工单数据列表
     * @param result        分析结果对象
     */
    private void analyzeProcessingTime(List<WorkOrderData> workOrderList, WorkOrderAnalysisResult result) {
        List<Integer> processingTimes = workOrderList.stream()
                .filter(wo -> wo.getWorkDurationMinutes() != null && wo.getWorkDurationMinutes() > 0)
                .map(WorkOrderData::getWorkDurationMinutes)
                .toList();

        if (!processingTimes.isEmpty()) {
            // 平均处理时长
            double average = processingTimes.stream()
                    .mapToInt(Integer::intValue)
                    .average()
                    .orElse(0.0);
            result.setAverageProcessingTime(average);

            // 最长处理时长
            result.setMaxProcessingTime(
                    processingTimes.stream()
                            .mapToInt(Integer::intValue)
                            .max()
                            .orElse(0)
            );

            // 最短处理时长
            result.setMinProcessingTime(
                    processingTimes.stream()
                            .mapToInt(Integer::intValue)
                            .min()
                            .orElse(0)
            );
        }
    }

    /**
     * 分析完成率
     *
     * @param workOrderList 工单数据列表
     * @param result        分析结果对象
     */
    private void analyzeCompletionRate(List<WorkOrderData> workOrderList, WorkOrderAnalysisResult result) {
        long totalCount = workOrderList.size();
        if (totalCount == 0) {
            return;
        }

        // 完成的工单数量（状态为"已完成"或类似）
        long completedCount = workOrderList.stream()
                .filter(wo -> StrUtil.isNotBlank(wo.getStatus()))
                .filter(wo -> wo.getStatus().contains("完成") || wo.getStatus().contains("已完成") ||
                        wo.getStatus().contains("结束") || wo.getStatus().contains("关闭"))
                .count();

        // 计算完成率
        double completionRate = (double) completedCount / totalCount * 100;
        result.setCompletionRate(completionRate);

        // 及时完成率（这里简化处理，实际应该根据预估时间和实际时间计算）
        long onTimeCompletedCount = workOrderList.stream()
                .filter(wo -> wo.getActualEndTime() != null && wo.getEstimatedEndTime() != null)
                .filter(wo -> !wo.getActualEndTime().isAfter(wo.getEstimatedEndTime()))
                .count();

        if (completedCount > 0) {
            double onTimeCompletionRate = (double) onTimeCompletedCount / completedCount * 100;
            result.setOnTimeCompletionRate(onTimeCompletionRate);
        }
    }

    /**
     * 分析收费情况
     *
     * @param workOrderList 工单数据列表
     * @param result        分析结果对象
     */
    private void analyzeChargeStatus(List<WorkOrderData> workOrderList, WorkOrderAnalysisResult result) {
        // 收费工单数量
        int chargedCount = (int) workOrderList.stream()
                .filter(wo -> StrUtil.isNotBlank(wo.getChargeStatus()))
                .filter(wo -> wo.getChargeStatus().contains("收费") || wo.getChargeStatus().contains("付费"))
                .count();

        // 免费工单数量
        int freeCount = (int) workOrderList.stream()
                .filter(wo -> StrUtil.isNotBlank(wo.getChargeStatus()))
                .filter(wo -> wo.getChargeStatus().contains("免费") || wo.getChargeStatus().contains("不收费"))
                .count();

        result.setChargedOrderCount(chargedCount);
        result.setFreeOrderCount(freeCount);
    }

    /**
     * 生成AI分析报告
     *
     * @param statisticalResult 统计分析结果
     * @return AI分析报告文本
     */
    private String generateAIAnalysis(WorkOrderAnalysisResult statisticalResult) {
        return generateAIAnalysis(statisticalResult, "fm/report-analysis1");
    }

    /**
     * 生成AI分析报告（支持指定提示词模板）
     *
     * @param statisticalResult 统计分析结果
     * @param promptTemplate    提示词模板路径
     * @return AI分析报告文本
     * @throws AIServiceException AI服务异常
     */
    private String generateAIAnalysis(WorkOrderAnalysisResult statisticalResult, String promptTemplate) {
        log.info("开始生成AI分析报告，模板: {}", promptTemplate);
        long startTime = System.currentTimeMillis();

        try {
            // 获取报表分析提示词模板
            String prompt = PromptsUtil.getPrompts(promptTemplate);
            if (StrUtil.isBlank(prompt)) {
                log.error("报表分析提示词模板为空或不存在，模板路径: {}", promptTemplate);
                throw new AIServiceException("AI分析模板配置错误", "REPORT_ANALYSIS", "TEMPLATE_NOT_FOUND");
            }

            // 将统计数据转换为JSON格式
            String statisticalDataJson = JSONUtil.toJsonPrettyStr(statisticalResult);
            log.debug("统计数据JSON长度: {} 字符", statisticalDataJson.length());

            // 验证统计数据
            if (statisticalDataJson.length() > 50000) { // 50KB限制
                log.warn("统计数据JSON过大，长度: {} 字符，可能影响AI分析性能", statisticalDataJson.length());
            }

            // 替换模板中的占位符
            String finalPrompt = prompt.replace("{{工单数据}}", statisticalDataJson);
            log.debug("最终提示词长度: {} 字符", finalPrompt.length());

            // 验证提示词长度
            if (finalPrompt.length() > 100000) { // 100KB限制
                log.warn("最终提示词过长，长度: {} 字符，可能导致AI服务异常", finalPrompt.length());
            }

            // 调用AI服务生成分析报告
            log.info("开始调用AI服务生成分析报告");
            String aiAnalysis = chatClient.prompt()
                    .user(finalPrompt)
                    .tools(chartTool)
                    .call()
                    .content();

            if (StrUtil.isBlank(aiAnalysis)) {
                log.warn("AI服务返回空的分析结果");
                throw new AIServiceException("AI服务返回空结果", "REPORT_ANALYSIS", "EMPTY_RESPONSE");
            }

            long processingTime = System.currentTimeMillis() - startTime;
            log.info("AI分析报告生成完成，报告长度: {} 字符，耗时: {}ms", aiAnalysis.length(), processingTime);
            return aiAnalysis;

        } catch (AIServiceException e) {
            throw e;
        } catch (ResourceAccessException e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("AI分析报告生成网络连接异常，耗时: {}ms, 错误: {}", processingTime, e.getMessage());
            throw new AIServiceException("AI服务网络连接异常: " + e.getMessage(), "REPORT_ANALYSIS", "CONNECTION_ERROR", null, e);

        } catch (RuntimeException e) {
            long processingTime = System.currentTimeMillis() - startTime;
            String errorMessage = e.getMessage();
            String errorCode = classifyAIServiceError(e);

            log.error("AI分析报告生成运行时异常，耗时: {}ms, 错误类型: {}, 错误: {}",
                    processingTime, errorCode, errorMessage, e);
            throw new AIServiceException("AI服务运行时异常: " + errorMessage, "REPORT_ANALYSIS", errorCode, null, e);

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("AI分析报告生成未知异常，耗时: {}ms, 错误: {}", processingTime, e.getMessage(), e);
            throw new AIServiceException("AI服务未知异常: " + e.getMessage(), "REPORT_ANALYSIS", "UNKNOWN_ERROR", null, e);
        }
    }

    /**
     * 验证AI服务是否可用
     *
     * @return true如果AI服务可用，false否则
     */
    private boolean isAIServiceAvailable() {
        try {
            // 发送一个简单的测试请求来验证AI服务是否可用
            String testResponse = chatClient.prompt()
                    .user("请回复'OK'")
                    .call()
                    .content();

            return StrUtil.isNotBlank(testResponse);
        } catch (Exception e) {
            log.warn("AI服务可用性检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 根据数据量选择合适的提示词模板
     *
     * @param dataCount 数据量
     * @return 提示词模板路径
     */
    private String selectPromptTemplate(int dataCount) {
//        if (dataCount > 1000) {
//            // 大数据量使用更详细的分析模板
//            return "fm/report-analysis3";
//        } else if (dataCount > 100) {
//            // 中等数据量使用标准分析模板
//            return "fm/report-analysis2";
//        } else {
//            // 小数据量使用基础分析模板
//            return "fm/report-analysis1";
//        }
        return "fm/report-analysis3";
    }

    /**
     * 生成分析ID
     *
     * @param fileId 文件ID
     * @return 分析ID
     */
    private String generateAnalysisId(Long fileId) {
        return "ANALYSIS_" + fileId + "_" + System.currentTimeMillis();
    }

    /**
     * 验证分析参数
     *
     * @param fileId     文件ID
     * @param analysisId 分析ID
     * @throws ReportAnalysisException 参数验证失败
     */
    private void validateAnalysisParameters(Long fileId, String analysisId) {
        log.debug("验证分析参数 - 分析ID: {}, 文件ID: {}", analysisId, fileId);

        if (fileId == null) {
            log.error("参数验证失败 - 文件ID为空 - 分析ID: {}", analysisId);
            throw new ReportAnalysisException("文件ID不能为空", null, "PARAMETER_VALIDATION", "NULL_FILE_ID");
        }

        if (fileId <= 0) {
            log.error("参数验证失败 - 文件ID无效 - 分析ID: {}, 文件ID: {}", analysisId, fileId);
            throw new ReportAnalysisException("文件ID必须大于0", fileId, "PARAMETER_VALIDATION", "INVALID_FILE_ID");
        }

        log.debug("参数验证通过 - 分析ID: {}, 文件ID: {}", analysisId, fileId);
    }

    /**
     * 获取文件实体并验证
     *
     * @param fileId     文件ID
     * @param analysisId 分析ID
     * @return 文件实体
     * @throws FileNotFoundException 文件不存在
     */
    private FileEntity getFileEntityWithValidation(Long fileId, String analysisId) throws FileNotFoundException {
        log.debug("获取文件实体 - 分析ID: {}, 文件ID: {}", analysisId, fileId);

        try {
            FileEntity fileEntity = fileService.getById(fileId);
            if (fileEntity == null) {
                log.error("文件实体不存在 - 分析ID: {}, 文件ID: {}", analysisId, fileId);
                throw new FileNotFoundException("文件不存在或已被删除，文件ID: " + fileId);
            }

            log.info("文件实体获取成功 - 分析ID: {}, 文件ID: {}, 文件名: {}, 文件路径: {}",
                    analysisId, fileId, fileEntity.getName(), fileEntity.getPath());

            return fileEntity;

        } catch (Exception e) {
            log.error("获取文件实体异常 - 分析ID: {}, 文件ID: {}, 错误: {}", analysisId, fileId, e.getMessage(), e);
            if (e instanceof FileNotFoundException) {
                throw e;
            }
            throw new ReportAnalysisException("获取文件实体失败: " + e.getMessage(), fileId, "FILE_RETRIEVAL", "DATABASE_ERROR", e);
        }
    }

    /**
     * 验证文件格式并记录日志
     *
     * @param fileEntity 文件实体
     * @param analysisId 分析ID
     * @throws UnsupportedFileFormatException 不支持的文件格式
     */
    private void validateFileFormatWithLogging(FileEntity fileEntity, String analysisId) {
        log.debug("验证文件格式 - 分析ID: {}, 文件名: {}, 文件类型: {}",
                analysisId, fileEntity.getName(), fileEntity.getType());

        try {
            validateFileFormat(fileEntity);
            log.info("文件格式验证通过 - 分析ID: {}, 文件名: {}", analysisId, fileEntity.getName());

        } catch (UnsupportedFileFormatException e) {
            log.warn("文件格式验证失败 - 分析ID: {}, 详细错误: {}", analysisId, e.getDetailedMessage());
            throw e;
        }
    }

    /**
     * 获取物理文件并验证
     *
     * @param fileEntity 文件实体
     * @param analysisId 分析ID
     * @return 物理文件对象
     * @throws FileNotFoundException 物理文件不存在
     */
    private File getPhysicalFileWithValidation(FileEntity fileEntity, String analysisId) throws FileNotFoundException {
        log.debug("获取物理文件 - 分析ID: {}, 文件名: {}", analysisId, fileEntity.getName());

        try {
            File csvFile = fileBizService.getFile(fileEntity);
            if (!csvFile.exists()) {
                log.error("物理文件不存在 - 分析ID: {}, 文件路径: {}", analysisId, csvFile.getAbsolutePath());
                throw new FileNotFoundException("物理文件不存在或已被删除，路径: " + csvFile.getAbsolutePath());
            }

            long fileSize = csvFile.length();
            log.info("物理文件获取成功 - 分析ID: {}, 文件路径: {}, 文件大小: {} bytes",
                    analysisId, csvFile.getAbsolutePath(), fileSize);

            // 检查文件大小
            if (fileSize == 0) {
                log.warn("物理文件为空 - 分析ID: {}, 文件路径: {}", analysisId, csvFile.getAbsolutePath());
            } else if (fileSize > 100 * 1024 * 1024) { // 100MB
                log.warn("物理文件较大 - 分析ID: {}, 文件大小: {} MB, 可能影响处理性能",
                        analysisId, fileSize / (1024 * 1024));
            }

            return csvFile;

        } catch (Exception e) {
            log.error("获取物理文件异常 - 分析ID: {}, 错误: {}", analysisId, e.getMessage(), e);
            if (e instanceof FileNotFoundException) {
                throw e;
            }
            throw new ReportAnalysisException("获取物理文件失败: " + e.getMessage(),
                    fileEntity.getId(), "FILE_ACCESS", "IO_ERROR", e);
        }
    }

    /**
     * 解析CSV文件（增强错误处理）
     *
     * @param csvFile    CSV文件
     * @param analysisId 分析ID
     * @return 工单数据列表
     * @throws CSVParseException CSV解析异常
     */
    private List<WorkOrderData> parseCSVFileWithEnhancedErrorHandling(File csvFile, String analysisId) {
        log.info("开始解析CSV文件 - 分析ID: {}, 文件名: {}, 文件大小: {} bytes",
                analysisId, csvFile.getName(), csvFile.length());

        List<WorkOrderData> workOrderList = new ArrayList<>();
        int totalRows;
        int successfulRows = 0;
        int failedRows = 0;

        try {
            // 使用Hutool的CsvReader读取CSV文件
            CsvReader reader = CsvUtil.getReader();
            cn.hutool.core.text.csv.CsvData csvData = reader.read(csvFile.toPath(), StandardCharsets.UTF_8);

            if (csvData == null) {
                log.error("CSV数据为空 - 分析ID: {}, 文件名: {}", analysisId, csvFile.getName());
                throw new CSVParseException("CSV文件读取失败，数据为空", csvFile.getName());
            }

            totalRows = csvData.getRowCount();
            if (totalRows == 0) {
                log.warn("CSV文件无数据行 - 分析ID: {}, 文件名: {}", analysisId, csvFile.getName());
                return workOrderList;
            }

            // 获取表头
            cn.hutool.core.text.csv.CsvRow headerRow = csvData.getRow(0);
            String[] headers = headerRow.toArray(new String[0]);
            log.info("CSV表头解析 - 分析ID: {}, 表头数量: {}, 表头内容: {}",
                    analysisId, headers.length, Arrays.toString(headers));

            // 验证表头
            validateCSVHeaders(headers, csvFile.getName(), analysisId);

            // 解析数据行
            for (int i = 1; i < totalRows; i++) {
                cn.hutool.core.text.csv.CsvRow dataRow = csvData.getRow(i);
                String[] row = dataRow.toArray(new String[0]);

                try {
                    WorkOrderData workOrder = parseWorkOrderFromRowWithValidation(headers, row, i + 1, analysisId);
                    if (workOrder != null) {
                        workOrderList.add(workOrder);
                        successfulRows++;
                    }
                } catch (Exception e) {
                    failedRows++;
                    log.warn("解析CSV行失败 - 分析ID: {}, 行号: {}, 错误: {}, 行数据: {}",
                            analysisId, i + 1, e.getMessage(), Arrays.toString(row));

                    // 如果失败行数过多，抛出异常
                    if (failedRows > totalRows * 0.5) { // 超过50%的行解析失败
                        log.error("CSV解析失败行数过多 - 分析ID: {}, 总行数: {}, 失败行数: {}, 失败率: {}%",
                                analysisId, totalRows - 1, failedRows, (failedRows * 100.0 / (totalRows - 1)));
                        throw new CSVParseException("CSV文件解析失败行数过多，可能文件格式不正确",
                                csvFile.getName(), i + 1);
                    }
                }
            }

            log.info("CSV文件解析完成 - 分析ID: {}, 总行数: {}, 成功解析: {}, 失败行数: {}, 成功率: {}%",
                    analysisId, totalRows - 1, successfulRows, failedRows,
                    (successfulRows * 100.0 / (totalRows - 1)));

            if (successfulRows == 0) {
                log.error("CSV文件无有效数据 - 分析ID: {}, 文件名: {}", analysisId, csvFile.getName());
                throw new CSVParseException("CSV文件无有效数据行", csvFile.getName());
            }

        } catch (CSVParseException e) {
            throw e;
        } catch (Exception e) {
            log.error("CSV文件解析异常 - 分析ID: {}, 文件名: {}, 错误: {}",
                    analysisId, csvFile.getName(), e.getMessage(), e);
            throw new CSVParseException("CSV文件解析失败: " + e.getMessage(), csvFile.getName(), null, null, e);
        }

        return workOrderList;
    }

    /**
     * 验证CSV表头
     *
     * @param headers    表头数组
     * @param fileName   文件名
     * @param analysisId 分析ID
     * @throws CSVParseException 表头验证失败
     */
    private void validateCSVHeaders(String[] headers, String fileName, String analysisId) {
        if (headers == null || headers.length == 0) {
            log.error("CSV表头为空 - 分析ID: {}, 文件名: {}", analysisId, fileName);
            throw new CSVParseException("CSV文件表头为空", fileName, 1);
        }

        // 检查是否包含必要的列
        Set<String> headerSet = Arrays.stream(headers)
                .map(String::trim)
                .collect(Collectors.toSet());

        List<String> missingHeaders = new ArrayList<>();
        String[] requiredHeaders = {"工单号", "申请人", "创建日期"};

        for (String required : requiredHeaders) {
            if (!headerSet.contains(required)) {
                missingHeaders.add(required);
            }
        }

        if (!missingHeaders.isEmpty()) {
            log.warn("CSV表头缺少必要列 - 分析ID: {}, 文件名: {}, 缺少列: {}",
                    analysisId, fileName, missingHeaders);
            // 这里只记录警告，不抛出异常，因为可能有其他格式的表头
        }

        log.debug("CSV表头验证通过 - 分析ID: {}, 表头数量: {}", analysisId, headers.length);
    }

    /**
     * 从CSV行数据解析工单对象（增强验证）
     *
     * @param headers    表头数组
     * @param row        数据行数组
     * @param rowNumber  行号
     * @param analysisId 分析ID
     * @return 工单数据对象
     */
    private WorkOrderData parseWorkOrderFromRowWithValidation(String[] headers, String[] row, int rowNumber, String analysisId) {
        if (row == null || row.length == 0) {
            log.debug("跳过空行 - 分析ID: {}, 行号: {}", analysisId, rowNumber);
            return null;
        }

        // 检查行数据长度
        if (row.length < headers.length) {
            log.debug("行数据列数不足 - 分析ID: {}, 行号: {}, 期望列数: {}, 实际列数: {}",
                    analysisId, rowNumber, headers.length, row.length);
        }

        WorkOrderData workOrder = new WorkOrderData();
        int processedFields = 0;

        for (int i = 0; i < headers.length && i < row.length; i++) {
            String header = headers[i].trim();
            String value = row[i].trim();

            // 跳过空值
            if (StrUtil.isBlank(value)) {
                continue;
            }

            // 根据表头映射设置字段值
            try {
                if (setWorkOrderFieldWithValidation(workOrder, header, value, rowNumber, analysisId)) {
                    processedFields++;
                }
            } catch (Exception e) {
                log.debug("设置字段失败 - 分析ID: {}, 行号: {}, 表头: {}, 值: {}, 错误: {}",
                        analysisId, rowNumber, header, value, e.getMessage());
            }
        }

        // 验证工单数据的完整性
        if (processedFields == 0) {
            log.debug("行数据无有效字段 - 分析ID: {}, 行号: {}", analysisId, rowNumber);
            return null;
        }

        // 检查关键字段
        if (StrUtil.isBlank(workOrder.getOrderNumber()) && StrUtil.isBlank(workOrder.getApplicant())) {
            log.debug("行数据缺少关键字段 - 分析ID: {}, 行号: {}", analysisId, rowNumber);
            return null;
        }

        return workOrder;
    }

    /**
     * 设置工单对象字段值（增强验证）
     *
     * @param workOrder  工单对象
     * @param header     表头名称
     * @param value      字段值
     * @param rowNumber  行号
     * @param analysisId 分析ID
     * @return 是否成功设置字段
     */
    private boolean setWorkOrderFieldWithValidation(WorkOrderData workOrder, String header, String value, int rowNumber, String analysisId) {
        String fieldName = CSV_COLUMN_MAPPING.get(header);
        if (StrUtil.isBlank(fieldName)) {
            return false; // 未映射的字段跳过
        }

        try {
            switch (fieldName) {
                case "orderNumber" -> {
                    workOrder.setOrderNumber(value);
                    return true;
                }
                case "applicant" -> {
                    workOrder.setApplicant(value);
                    return true;
                }
                case "createDate" -> {
                    LocalDate date = parseDate(value);
                    workOrder.setCreateDate(date);
                    if (date == null) {
                        log.debug("日期解析失败 - 分析ID: {}, 行号: {}, 字段: {}, 值: {}",
                                analysisId, rowNumber, header, value);
                    }
                    return date != null;
                }
                case "createTime", "publishTime", "acceptTime", "estimatedStartTime",
                     "estimatedEndTime", "actualStartTime", "actualEndTime" -> {
                    LocalTime time = parseTime(value);
                    switch (fieldName) {
                        case "createTime" -> workOrder.setCreateTime(time);
                        case "publishTime" -> workOrder.setPublishTime(time);
                        case "acceptTime" -> workOrder.setAcceptTime(time);
                        case "estimatedStartTime" -> workOrder.setEstimatedStartTime(time);
                        case "estimatedEndTime" -> workOrder.setEstimatedEndTime(time);
                        case "actualStartTime" -> workOrder.setActualStartTime(time);
                        case "actualEndTime" -> workOrder.setActualEndTime(time);
                    }
                    if (time == null) {
                        log.debug("时间解析失败 - 分析ID: {}, 行号: {}, 字段: {}, 值: {}",
                                analysisId, rowNumber, header, value);
                    }
                    return time != null;
                }
                case "workDurationMinutes" -> {
                    Integer duration = parseInteger(value);
                    workOrder.setWorkDurationMinutes(duration);
                    if (duration == null) {
                        log.debug("整数解析失败 - 分析ID: {}, 行号: {}, 字段: {}, 值: {}",
                                analysisId, rowNumber, header, value);
                    }
                    return duration != null;
                }
                default -> {
                    // 字符串字段
                    switch (fieldName) {
                        case "publisher" -> workOrder.setPublisher(value);
                        case "executor" -> workOrder.setExecutor(value);
                        case "serviceType" -> workOrder.setServiceType(value);
                        case "priority" -> workOrder.setPriority(value);
                        case "department" -> workOrder.setDepartment(value);
                        case "building" -> workOrder.setBuilding(value);
                        case "floor" -> workOrder.setFloor(value);
                        case "room" -> workOrder.setRoom(value);
                        case "fullLocation" -> workOrder.setFullLocation(value);
                        case "description" -> workOrder.setDescription(value);
                        case "workContent" -> workOrder.setWorkContent(value);
                        case "status" -> workOrder.setStatus(value);
                        case "pauseReason" -> workOrder.setPauseReason(value);
                        case "chargeStatus" -> workOrder.setChargeStatus(value);
                    }
                    return true;
                }
            }
        } catch (Exception e) {
            log.debug("字段设置异常 - 分析ID: {}, 行号: {}, 字段: {}, 值: {}, 错误: {}",
                    analysisId, rowNumber, header, value, e.getMessage());
            return false;
        }
    }

    /**
     * 执行统计分析（增强错误处理）
     *
     * @param workOrderList 工单数据列表
     * @param analysisId    分析ID
     * @return 统计分析结果
     * @throws ReportAnalysisException 统计分析异常
     */
    private WorkOrderAnalysisResult performStatisticalAnalysisWithErrorHandling(List<WorkOrderData> workOrderList, String analysisId) {
        log.info("开始执行统计分析 - 分析ID: {}, 数据量: {}", analysisId, workOrderList.size());

        try {
            WorkOrderAnalysisResult result = performStatisticalAnalysis(workOrderList);

            log.info("统计分析完成 - 分析ID: {}, 总数量: {}, 状态分布: {}, 优先级分布: {}",
                    analysisId, result.getTotalCount(),
                    result.getStatusDistribution() != null ? result.getStatusDistribution().size() : 0,
                    result.getPriorityDistribution() != null ? result.getPriorityDistribution().size() : 0);

            return result;

        } catch (Exception e) {
            log.error("统计分析异常 - 分析ID: {}, 错误: {}", analysisId, e.getMessage(), e);
            throw new ReportAnalysisException("统计分析失败: " + e.getMessage(), null, "STATISTICAL_ANALYSIS", "CALCULATION_ERROR", e);
        }
    }

    /**
     * 生成AI分析报告（增强错误处理）
     *
     * @param statisticalResult 统计分析结果
     * @param promptTemplate    提示词模板
     * @param analysisId        分析ID
     * @return AI分析报告文本
     * @throws AIServiceException AI服务异常
     */
    private String generateAIAnalysisWithEnhancedErrorHandling(WorkOrderAnalysisResult statisticalResult, String promptTemplate, String analysisId) {
        log.info("开始生成AI分析报告 - 分析ID: {}, 模板: {}", analysisId, promptTemplate);
        long startTime = System.currentTimeMillis();

        try {
            String aiAnalysis = generateAIAnalysis(statisticalResult, promptTemplate);

            long processingTime = System.currentTimeMillis() - startTime;
            log.info("AI分析报告生成完成 - 分析ID: {}, 报告长度: {} 字符, 耗时: {}ms",
                    analysisId, aiAnalysis.length(), processingTime);

            return aiAnalysis;

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("AI分析报告生成异常 - 分析ID: {}, 耗时: {}ms, 错误: {}",
                    analysisId, processingTime, e.getMessage(), e);

            // 根据异常类型进行分类处理
            String errorType = classifyAIServiceError(e);
            throw new AIServiceException("AI分析报告生成失败: " + e.getMessage(), "REPORT_ANALYSIS", errorType, analysisId, e);
        }
    }

    /**
     * 分类AI服务错误
     *
     * @param exception 异常对象
     * @return 错误类型
     */
    private String classifyAIServiceError(Exception exception) {
        String errorMessage = exception.getMessage();
        if (errorMessage == null) {
            return "UNKNOWN_ERROR";
        }

        errorMessage = errorMessage.toLowerCase();

        if (errorMessage.contains("timeout") || errorMessage.contains("超时")) {
            return "TIMEOUT_ERROR";
        } else if (errorMessage.contains("connection") || errorMessage.contains("连接")) {
            return "CONNECTION_ERROR";
        } else if (errorMessage.contains("retry") || errorMessage.contains("重试")) {
            return "RETRY_EXHAUSTED";
        } else if (errorMessage.contains("network") || errorMessage.contains("网络")) {
            return "NETWORK_ERROR";
        } else if (errorMessage.contains("authentication") || errorMessage.contains("认证")) {
            return "AUTH_ERROR";
        } else if (errorMessage.contains("quota") || errorMessage.contains("限额")) {
            return "QUOTA_EXCEEDED";
        } else if (errorMessage.contains("rate limit") || errorMessage.contains("频率限制")) {
            return "RATE_LIMIT";
        } else {
            return "SERVICE_ERROR";
        }
    }

    /**
     * 构建分析结果
     *
     * @param statisticalResult 统计分析结果
     * @param aiAnalysis        AI分析报告
     * @param fileEntity        文件实体
     * @param dataCount         数据数量
     * @param startTime         开始时间
     * @param analysisId        分析ID
     * @return 报表分析结果
     */
    private ReportAnalysisResult buildAnalysisResult(WorkOrderAnalysisResult statisticalResult, String aiAnalysis,
                                                     FileEntity fileEntity, int dataCount, long startTime, String analysisId) {
        log.debug("构建分析结果 - 分析ID: {}", analysisId);

        ReportAnalysisResult result = new ReportAnalysisResult();
        result.setStatisticalData(statisticalResult);
        result.setAiAnalysis(aiAnalysis);
        result.setFileName(fileEntity.getName());
        result.setAnalysisTime(LocalDateTime.now());
        result.setDataCount(dataCount);
        result.setProcessingTimeMs(System.currentTimeMillis() - startTime);
        result.setAnalysisStatus("SUCCESS");
        result.setRemarks("分析完成 - 分析ID: " + analysisId);

        log.debug("分析结果构建完成 - 分析ID: {}", analysisId);
        return result;
    }

    /**
     * 统一处理分析异常，返回用户友好的错误信息
     * 
     * @param exception 异常对象
     * @return RestResult错误响应
     */
    public RestResult<ReportAnalysisResult> handleAnalysisException(Exception exception) {
        String requestId = generateRequestId();
        long startTime = System.currentTimeMillis();
        
        try {
            if (exception instanceof FileNotFoundException) {
                long processingTime = System.currentTimeMillis() - startTime;
                log.error("报表分析失败 - 文件不存在 - 请求ID: {}, 耗时: {}ms, 错误: {}", 
                        requestId, processingTime, exception.getMessage());
                return new RestResult<>(RestResult.Status.NOT_FOUND, "文件不存在或已被删除", null);
                
            } else if (exception instanceof UnsupportedFileFormatException e) {
                long processingTime = System.currentTimeMillis() - startTime;
                log.error("报表分析失败 - 文件格式不支持 - 请求ID: {}, 耗时: {}ms, 详细错误: {}", 
                        requestId, processingTime, e.getDetailedMessage());
                return new RestResult<>(RestResult.Status.PARAM_ERROR, 
                        String.format("不支持的文件格式，仅支持CSV文件。文件名: %s, 格式: %s", 
                                e.getFileName(), e.getFileFormat()), null);
                
            } else if (exception instanceof CSVParseException e) {
                long processingTime = System.currentTimeMillis() - startTime;
                log.error("报表分析失败 - CSV解析异常 - 请求ID: {}, 耗时: {}ms, 详细错误: {}", 
                        requestId, processingTime, e.getDetailedMessage());
                
                String userMessage = buildCSVParseErrorMessage(e);
                return new RestResult<>(RestResult.Status.FAIL, userMessage, null);
                
            } else if (exception instanceof AIServiceException e) {
                long processingTime = System.currentTimeMillis() - startTime;
                log.error("报表分析失败 - AI服务异常 - 请求ID: {}, 耗时: {}ms, 详细错误: {}", 
                        requestId, processingTime, e.getDetailedMessage());
                
                String userMessage = getAIServiceErrorMessage(e);
                return new RestResult<>(RestResult.Status.FAIL, userMessage, null);
                
            } else if (exception instanceof ReportAnalysisException e) {
                long processingTime = System.currentTimeMillis() - startTime;
                log.error("报表分析失败 - 业务逻辑异常 - 请求ID: {}, 耗时: {}ms, 详细错误: {}", 
                        requestId, processingTime, e.getDetailedMessage());
                
                String userMessage = getReportAnalysisErrorMessage(e);
                return new RestResult<>(RestResult.Status.FAIL, userMessage, null);
                
            } else if (exception instanceof RuntimeException) {
                long processingTime = System.currentTimeMillis() - startTime;
                log.error("报表分析失败 - 运行时异常 - 请求ID: {}, 耗时: {}ms, 错误: {}", 
                        requestId, processingTime, exception.getMessage(), exception);
                
                return new RestResult<>(RestResult.Status.FAIL, "系统内部错误，请稍后重试", null);
                
            } else {
                long processingTime = System.currentTimeMillis() - startTime;
                log.error("报表分析失败 - 未知异常 - 请求ID: {}, 耗时: {}ms, 错误: {}", 
                        requestId, processingTime, exception.getMessage(), exception);
                return new RestResult<>(RestResult.Status.FAIL, "系统发生未知错误，请联系管理员", null);
            }
        } catch (Exception e) {
            log.error("异常处理过程中发生错误 - 请求ID: {}, 原始异常: {}, 处理异常: {}", 
                    requestId, exception.getMessage(), e.getMessage(), e);
            return new RestResult<>(RestResult.Status.FAIL, "系统发生未知错误，请联系管理员", null);
        }
    }

    /**
     * 生成请求ID
     * 
     * @return 请求ID
     */
    private String generateRequestId() {
        return "REQ_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }

    /**
     * 构建CSV解析错误消息
     * 
     * @param e CSV解析异常
     * @return 用户友好的错误消息
     */
    private String buildCSVParseErrorMessage(CSVParseException e) {
        String userMessage = "CSV文件格式错误或数据异常";
        if (e.getErrorLineNumber() != null) {
            userMessage += String.format("，错误位置：第%d行", e.getErrorLineNumber());
        }
        if (e.getErrorColumnName() != null) {
            userMessage += String.format("，错误列：%s", e.getErrorColumnName());
        }
        userMessage += "，请检查文件内容格式";
        return userMessage;
    }

    /**
     * 获取AI服务异常的用户友好错误消息
     * 
     * @param e AI服务异常
     * @return 用户友好的错误消息
     */
    private String getAIServiceErrorMessage(AIServiceException e) {
        String errorCode = e.getErrorCode();
        if (errorCode == null) {
            return "AI分析服务暂时不可用，请稍后重试";
        }
        
        return switch (errorCode) {
            case "TIMEOUT_ERROR" -> "AI分析服务响应超时，请稍后重试";
            case "CONNECTION_ERROR" -> "AI分析服务连接异常，请检查网络连接或稍后重试";
            case "RETRY_EXHAUSTED" -> "AI分析服务暂时不可用（重试次数已耗尽），请稍后重试";
            case "NETWORK_ERROR" -> "网络连接异常，请检查网络连接";
            case "AUTH_ERROR" -> "AI服务认证失败，请联系管理员";
            case "QUOTA_EXCEEDED" -> "AI服务使用额度已超限，请联系管理员";
            case "RATE_LIMIT" -> "AI服务请求频率过高，请稍后重试";
            default -> "AI分析服务异常，请稍后重试";
        };
    }

    /**
     * 获取报表分析异常的用户友好错误消息
     * 
     * @param e 报表分析异常
     * @return 用户友好的错误消息
     */
    private String getReportAnalysisErrorMessage(ReportAnalysisException e) {
        String errorType = e.getErrorType();
        String analysisPhase = e.getAnalysisPhase();
        
        if (errorType == null) {
            return "报表分析失败，请稍后重试";
        }
        
        return switch (errorType) {
            case "NULL_FILE_ID", "INVALID_FILE_ID" -> "文件ID参数错误";
            case "DATABASE_ERROR" -> "数据库访问异常，请稍后重试";
            case "IO_ERROR" -> "文件读取异常，请检查文件是否完整";
            case "PARSE_ERROR" -> "文件解析失败，请检查文件格式";
            case "CALCULATION_ERROR" -> "数据统计计算异常，请检查数据完整性";
            case "SERVICE_ERROR" -> "服务调用异常，请稍后重试";
            case "SYSTEM_ERROR" -> "系统内部错误，请联系管理员";
            default -> {
                switch (analysisPhase) {
                    case "CSV_PARSING" -> {
                        yield "CSV文件解析失败，请检查文件格式";
                    }
                    case "AI_ANALYSIS" -> {
                        yield "AI分析失败，请稍后重试";
                    }
                    case "STATISTICAL_ANALYSIS" -> {
                        yield "数据统计分析失败，请检查数据完整性";
                    }
                    case null, default -> {
                        yield "报表分析失败，请稍后重试";
                    }
                }
            }
        };
    }
}